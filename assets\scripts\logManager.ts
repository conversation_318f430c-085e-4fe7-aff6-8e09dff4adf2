import { LogConfig } from './config';

/**
 * 日志管理器
 * 统一管理所有模块的日志输出，支持模块级别的开关控制
 */
export class LogManager {
    
    /**
     * 游戏管理器日志
     */
    static gameManager = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.gameManager) {
                console.log(`🎮 [GameManager] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.gameManager) {
                console.warn(`🎮 [GameManager] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🎮 [GameManager] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 连接系统日志
     */
    static connectionSystem = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.connectionSystem) {
                console.log(`🔗 [Connection] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.connectionSystem) {
                console.warn(`🔗 [Connection] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🔗 [Connection] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 选择逻辑日志
     */
    static selectionLogic = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.selectionLogic) {
                console.log(`👆 [Selection] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.selectionLogic) {
                console.warn(`👆 [Selection] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`👆 [Selection] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 动画状态日志
     */
    static animationState = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.animationState) {
                console.log(`🎬 [Animation] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.animationState) {
                console.warn(`🎬 [Animation] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🎬 [Animation] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 无解检测器日志
     */
    static deadlockDetector = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.deadlockDetector) {
                console.log(`🔍 [Deadlock] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.deadlockDetector) {
                console.warn(`🔍 [Deadlock] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🔍 [Deadlock] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 洗牌算法日志
     */
    static shuffleAlgorithm = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.shuffleAlgorithm) {
                console.log(`🔀 [Shuffle] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.shuffleAlgorithm) {
                console.warn(`🔀 [Shuffle] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🔀 [Shuffle] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 菜单管理器日志
     */
    static menuManager = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.menuManager) {
                console.log(`🎛️ [Menu] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.menuManager) {
                console.warn(`🎛️ [Menu] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🎛️ [Menu] ${message}`, ...args);
            }
        }
    };
    
    /**
     * Toast系统日志
     */
    static toastSystem = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.toastSystem) {
                console.log(`🍞 [Toast] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.toastSystem) {
                console.warn(`🍞 [Toast] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🍞 [Toast] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 音频管理器日志
     */
    static audioManager = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.audioManager) {
                console.log(`🔊 [Audio] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.audioManager) {
                console.warn(`🔊 [Audio] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🔊 [Audio] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 调试信息日志
     */
    static debug = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.debugInfo) {
                console.log(`🐛 [Debug] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.debugInfo) {
                console.warn(`🐛 [Debug] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`🐛 [Debug] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 性能监控日志
     */
    static performance = {
        log: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.performance) {
                console.log(`⚡ [Performance] ${message}`, ...args);
            }
        },
        warn: (message: string, ...args: any[]) => {
            if (LogConfig.enableAllLogs || LogConfig.performance) {
                console.warn(`⚡ [Performance] ${message}`, ...args);
            }
        },
        error: (message: string, ...args: any[]) => {
            if (LogConfig.enableErrorLogs) {
                console.error(`⚡ [Performance] ${message}`, ...args);
            }
        }
    };
    
    /**
     * 通用日志方法（用于临时调试）
     */
    static temp = {
        log: (message: string, ...args: any[]) => {
            console.log(`🔧 [Temp] ${message}`, ...args);
        },
        warn: (message: string, ...args: any[]) => {
            console.warn(`🔧 [Temp] ${message}`, ...args);
        },
        error: (message: string, ...args: any[]) => {
            console.error(`🔧 [Temp] ${message}`, ...args);
        }
    };
    
    /**
     * 批量设置日志开关
     */
    static setLogLevel(level: 'all' | 'errors' | 'none') {
        switch (level) {
            case 'all':
                LogConfig.enableAllLogs = true;
                console.log('🔧 [LogManager] 已启用所有日志');
                break;
            case 'errors':
                LogConfig.enableAllLogs = false;
                LogConfig.enableErrorLogs = true;
                console.log('🔧 [LogManager] 只显示错误日志');
                break;
            case 'none':
                LogConfig.enableAllLogs = false;
                LogConfig.enableErrorLogs = false;
                console.log('🔧 [LogManager] 已关闭所有日志');
                break;
        }
    }
}
