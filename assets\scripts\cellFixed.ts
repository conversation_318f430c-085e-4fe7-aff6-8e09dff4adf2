import { _decorator, Component, Node, Graphics, Color, UITransform, Sprite, SpriteAtlas, Label, CCInteger } from 'cc';
import { gameMgr } from './gameMgr';
const { ccclass, property } = _decorator;

/**
 * 修复版本的棋子组件
 */
@ccclass('cellFixed')
export class cellFixed extends Component {
    @property({ type: SpriteAtlas })
    fruitAtlas: SpriteAtlas = null!;

    @property({ type: CCInteger, displayName: "棋子大小" })
    pieceSize: number = 60;

    @property({ type: CCInteger, displayName: "圆角半径" })
    cornerRadius: number = 15;

    @property({ type: [Color] })
    backgroundColors: Color[] = [
        new Color(255, 220, 220), // 浅红色背景
        new Color(220, 255, 220), // 浅绿色背景
        new Color(220, 220, 255), // 浅蓝色背景
        new Color(255, 255, 220), // 浅黄色背景
        new Color(255, 220, 255), // 浅紫色背景
        new Color(220, 255, 255), // 浅青色背景
        new Color(255, 235, 220), // 浅橙色背景
        new Color(235, 255, 235), // 很浅绿色背景
        new Color(255, 240, 240), // 很浅粉色背景
        new Color(240, 240, 255), // 很浅蓝色背景
        new Color(255, 255, 240), // 很浅黄色背景
    ];

    private fruitType: number = 1;
    private graphics: Graphics = null!;

    onLoad() {
        // 确保在最早的时机初始化Graphics组件
        this.initGraphics();
    }

    start() {
        this.initCell();
    }

    /**
     * 初始化Graphics组件
     */
    private initGraphics() {
        console.log("初始化Graphics组件...");
        
        // 获取或添加Graphics组件
        this.graphics = this.node.getComponent(Graphics);
        if (!this.graphics) {
            this.graphics = this.node.addComponent(Graphics);
            console.log("✓ 已添加Graphics组件");
        } else {
            console.log("✓ Graphics组件已存在");
        }
    }

    /**
     * 初始化棋子
     */
    private initCell() {
        console.log("=== 初始化Cell开始 ===");
        
        // 设置节点大小
        const uiTransform = this.node.getComponent(UITransform);
        if (uiTransform) {
            uiTransform.setContentSize(this.pieceSize, this.pieceSize);
            console.log("✓ UITransform大小设置为:", this.pieceSize);
        }

        // 绘制背景
        this.drawBackground();
        
        console.log("=== 初始化Cell完成 ===");
    }

    /**
     * 绘制背景
     */
    private drawBackground() {
        if (!this.graphics) {
            console.error("❌ Graphics组件不存在");
            return;
        }

        console.log("🎨 绘制背景，类型:", this.fruitType);

        // 清除之前的绘制
        this.graphics.clear();

        // 获取背景颜色
        const colorIndex = gameMgr.getBackgroundColorIndex(this.fruitType);
        const backgroundColor = this.backgroundColors[colorIndex] || this.backgroundColors[0];

        // 设置填充颜色
        this.graphics.fillColor = backgroundColor;

        // 绘制圆角矩形
        const halfSize = this.pieceSize / 2;
        this.graphics.roundRect(-halfSize, -halfSize, this.pieceSize, this.pieceSize, this.cornerRadius);
        this.graphics.fill();

        // 添加边框
        this.graphics.strokeColor = new Color(0, 0, 0, 80);
        this.graphics.lineWidth = 1.5;
        this.graphics.roundRect(-halfSize, -halfSize, this.pieceSize, this.pieceSize, this.cornerRadius);
        this.graphics.stroke();

        console.log("✓ 背景绘制完成");
    }

    /**
     * 更新图标
     */
    private updateIcon() {
        if (!this.fruitAtlas) {
            this.createSimpleLabel();
            return;
        }

        // 获取或创建icon节点
        let iconNode = this.node.getChildByName('icon');
        if (!iconNode) {
            iconNode = this.createIconNode();
        }

        const sprite = iconNode.getComponent(Sprite);
        if (sprite) {
            const spriteFrameName = "icon_" + this.fruitType;
            const spriteFrame = this.fruitAtlas.getSpriteFrame(spriteFrameName);
            
            if (spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                console.log("✓ 图标设置成功:", spriteFrameName);
            } else {
                console.warn("⚠️ 未找到图标:", spriteFrameName);
                this.createSimpleLabel();
            }
        }
    }

    /**
     * 创建图标节点
     */
    private createIconNode(): Node {
        const iconNode = new Node('icon');
        iconNode.addComponent(Sprite);
        
        const uiTransform = iconNode.addComponent(UITransform);
        const iconSize = this.pieceSize * 0.7;
        uiTransform.setContentSize(iconSize, iconSize);
        
        iconNode.parent = this.node;
        iconNode.setSiblingIndex(1); // 确保在Graphics之上
        
        console.log("✓ 创建图标节点，大小:", iconSize);
        return iconNode;
    }

    /**
     * 创建简单文字标签（当没有图标资源时）
     */
    private createSimpleLabel() {
        // 移除旧标签
        const oldLabel = this.node.getChildByName('label');
        if (oldLabel) {
            oldLabel.destroy();
        }

        // 创建新标签
        const labelNode = new Node('label');
        const label = labelNode.addComponent(Label);
        label.string = this.fruitType.toString();
        label.fontSize = 24;
        label.color = new Color(0, 0, 0, 255);

        const uiTransform = labelNode.addComponent(UITransform);
        uiTransform.setContentSize(40, 40);

        labelNode.parent = this.node;
        console.log("✓ 创建文字标签:", this.fruitType);
    }

    /**
     * 设置棋子类型
     */
    public setType(type: number) {
        console.log("设置棋子类型:", type);
        this.fruitType = type;
        
        // 重新绘制背景和图标
        this.drawBackground();
        this.updateIcon();
    }

    /**
     * 获取棋子类型
     */
    public getType(): number {
        return this.fruitType;
    }

    /**
     * 设置棋子大小
     */
    public setPieceSize(newSize: number) {
        this.pieceSize = newSize;
        
        // 更新UITransform
        const uiTransform = this.node.getComponent(UITransform);
        if (uiTransform) {
            uiTransform.setContentSize(this.pieceSize, this.pieceSize);
        }
        
        // 重新绘制
        this.drawBackground();
        this.updateIcon();
    }
}
