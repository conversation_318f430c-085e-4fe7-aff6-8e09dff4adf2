//设备宽高
export const deviceWidth: number = 720;
export const deviceHeight: number = 1280;

// //棋盘宽高
// export const gridWidth: number = 700;
// export const gridHeight: number = 700;

//棋子间距（控制棋子之间的距离）
export const cellSpacing: number = 75;

//棋子默认大小（可在编辑器中覆盖）
export const defaultCellSize: number = 100;

//水果种类最小值
export const minFruitType: number = 1;
//水果种类最大值
export const maxFruitType: number = 11;

//关卡数=第几关
export const levelNum: number = 3;

// ==================== 日志控制配置 ====================

/**
 * 日志控制开关
 * 设置为 false 可以关闭对应模块的日志输出
 */
export const LogConfig = {
    // 游戏核心模块
    gameManager: true,          // 游戏管理器日志
    connectionSystem: false,    // 连接系统日志（输出较多，建议关闭）
    selectionLogic: false,      // 选择逻辑日志
    animationState: false,      // 动画状态日志

    // 检测和算法模块
    deadlockDetector: false,     // 无解检测日志
    shuffleAlgorithm: false,    // 洗牌算法详细日志

    // UI和交互模块
    menuManager: true,          // 菜单管理器日志
    toastSystem: true,          // Toast提示系统日志

    // 音频模块
    audioManager: false,        // 音频管理器日志

    // 调试模块
    debugInfo: false,           // 调试信息输出
    performance: false,         // 性能监控日志

    // 全局开关
    enableAllLogs: false,       // 设置为 true 时启用所有日志
    enableErrorLogs: true,      // 错误日志始终启用
};