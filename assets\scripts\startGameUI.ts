import { _decorator, Component } from 'cc';
const { ccclass } = _decorator;
import { AudioManager } from './audioManager';
import { VibrationSystem } from './vibrationSystem';
import { gameMgrSimplified } from './gameMgrSimplified';

/**
 * 开始游戏UI控制器
 * 专门用于loading界面的按钮交互
 */
@ccclass('StartGameUI')
export class StartGameUI extends Component {
    
    // ==================== 系统引用 ====================
    private audioManager: AudioManager = null;
    private vibrationSystem: VibrationSystem = null;
    private gameManager: gameMgrSimplified = null;
    
    // ==================== 生命周期方法 ====================
    
    start() {
        console.log("🎮 StartGameUI 开始初始化");
        
        // 初始化系统引用
        this.audioManager = AudioManager.getInstance();
        this.vibrationSystem = VibrationSystem.getInstance();
        
        // 查找游戏管理器
        this.gameManager = this.node.scene.getComponentInChildren(gameMgrSimplified);
        
        if (this.gameManager) {
            console.log("✅ 找到游戏管理器");
        } else {
            console.error("❌ 未找到游戏管理器");
        }
        
        // 播放等待音效
        this.playWaitingAudio();
        
        console.log("🎮 StartGameUI 初始化完成");
        console.log("🎮 Loading界面已显示");
    }
    
    // ==================== 音效控制 ====================
    
    /**
     * 播放等待音效
     */
    private playWaitingAudio() {
        if (this.audioManager) {
            this.audioManager.playLoadingMusic();
        }
    }
    
    /**
     * 播放点击音效
     */
    private playClickAudio() {
        if (this.audioManager) {
            this.audioManager.playClickSound();
        }
    }
    
    // ==================== 震动控制 ====================
    
    /**
     * 触发震动反馈
     */
    private triggerVibration() {
        if (this.vibrationSystem) {
            this.vibrationSystem.vibrate([50]); // 50ms轻微震动
        }
    }
    
    // ==================== 公共方法 ====================
    
    /**
     * 开始游戏（供按钮绑定使用）
     * 这是主要的入口函数
     */
    public startGame() {
        console.log("🎮 点击开始游戏按钮");
        
        // 播放点击音效
        this.playClickAudio();
        
        // 触发震动反馈
        this.triggerVibration();
        
        // 销毁loading界面
        this.destroyLoadingUI();
        
        // 通知游戏管理器开始游戏
        this.notifyGameStart();
    }
    
    /**
     * 销毁loading界面
     */
    private destroyLoadingUI() {
        console.log("🗑️ 销毁loading界面");
        
        // 延迟一点时间让音效和震动完成
        this.scheduleOnce(() => {
            if (this.node && this.node.isValid) {
                this.node.destroy();
                console.log("✅ loading界面已销毁");
            }
        }, 0.1);
    }
    
    /**
     * 通知游戏管理器开始游戏
     */
    private notifyGameStart() {
        if (this.gameManager) {
            console.log("🎮 通知游戏管理器开始游戏");
            
            // 延迟一点时间确保loading界面销毁完成
            this.scheduleOnce(() => {
                this.gameManager.enterGame();
            }, 0.15);
        } else {
            console.error("❌ 游戏管理器未找到，无法开始游戏");
        }
    }
    
    // ==================== 生命周期清理 ====================
    
    onDestroy() {
        console.log("🎮 StartGameUI 组件被销毁");
        
        // 清理引用
        this.audioManager = null;
        this.vibrationSystem = null;
        this.gameManager = null;
    }
}
