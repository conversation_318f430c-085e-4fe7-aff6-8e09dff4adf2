import { _decorator, Component, Node, Prefab, Button } from 'cc';
const { ccclass, property } = _decorator;
import { lp } from './loadPrefab';
import { cellSpacing, maxFruitType, minFruitType, levelNum } from './config';
import { gameData } from './gameData';
import { cellFixed } from './cellFixed';
import { AudioManager } from './audioManager';
import { ConnectionSystem } from './connectionSystem';
import { VibrationSystem } from './vibrationSystem';
import { SelectionLogicManager } from './selectionLogicManager';
import { deadlockDetector } from './deadlockDetector';
import { simpleToast } from './simpleToast';
import { LogManager } from './logManager';

/**
 * 精简版游戏管理器 - 主要负责游戏初始化和协调各个系统
 * <EMAIL>
 */
@ccclass('gameMgrSimplified')
export class gameMgrSimplified extends Component {
    
    // ==================== 预制体属性 ====================
    @property({type: Prefab})
    backgroundPrefab: Prefab = null;
    @property({type: Prefab})
    headerPrefab: Prefab = null;
    @property({type: Prefab})
    gridPrefab: Prefab = null;
    @property({type: Prefab})
    menuPrefab: Prefab = null;
    @property({type: Prefab})
    cellPrefab: Prefab = null;
    @property({type: Prefab})
    loadingPrefab: Prefab = null;

    // ==================== 游戏状态 ====================
    private gridNode: Node = null;
    private levelIndex: number = levelNum - 1;
    private gameStarted: boolean = false; // 游戏是否已经开始
    
    // ==================== 系统管理器 ====================
    private audioManager: AudioManager = null;
    private connectionSystem: ConnectionSystem = null;
    private vibrationSystem: VibrationSystem = null;
    private selectionLogicManager: SelectionLogicManager = null;
    private deadlockDetector: deadlockDetector = null;
    private toastManager: simpleToast = null;
    
    // ==================== 颜色映射系统 ====================
    private static colorMapping: Map<number, number> = new Map();
    private static readonly BACKGROUND_COLOR_COUNT = 11;
    private static colorAssignmentCounter = 0;

    // ==================== 单例模式 ====================
    private static instance: gameMgrSimplified = null;

    /**
     * 获取游戏管理器单例实例
     */
    public static getInstance(): gameMgrSimplified {
        return gameMgrSimplified.instance;
    }

    // ==================== 生命周期方法 ====================
    
    start() {
        // 设置单例实例
        gameMgrSimplified.instance = this;

        this.initializeSystems();
        //加载Loading页面
        lp(this.loadingPrefab, this.node);

    }

    // ==================== 初始化方法 ====================
    enterGame() {
        // 防止重复进入游戏
        if (this.gameStarted) {
            console.log("⚠️ 游戏已经开始，忽略重复调用");
            return;
        }

        console.log("🎮 开始进入游戏主界面");
        this.gameStarted = true;

        // 直接加载游戏主界面
        this.loadUI();

        // 初始化游戏网格
        this.initGrid();

        // 开始游戏音频
        this.startGameAudio();

        console.log("🎮 游戏主界面加载完成");
    }
    


    
    /**
     * 初始化所有系统
     */
    private initializeSystems() {
        this.audioManager = AudioManager.getInstance();
        this.vibrationSystem = VibrationSystem.getInstance();
        this.vibrationSystem.initialize();

        // 初始化Toast管理器
        this.toastManager = this.node.addComponent(simpleToast);

        // 初始化无解检测器
        this.deadlockDetector = this.node.addComponent(deadlockDetector);

        LogManager.gameManager.log("🔧 所有系统初始化完成");
    }
    
    /**
     * 加载UI界面
     */
    private loadUI() {
        lp(this.backgroundPrefab, this.node);
        lp(this.headerPrefab, this.node);
        this.gridNode = lp(this.gridPrefab, this.node);
        lp(this.menuPrefab, this.node);
    }
    
    /**
     * 开始游戏音频
     */
    private startGameAudio() {
        this.scheduleOnce(() => {
            if (this.audioManager) {
                this.audioManager.onGameStart();
            }
        }, 2.0);
    }

    // ==================== 棋盘初始化 ====================
    
    /**
     * 初始化棋盘
     */
    initGrid() {
        gameMgrSimplified.resetColorMapping();
        
        const currentLevel = gameData[this.levelIndex];
        let cellCount = 0;
        let randomType = 0;

        for (let i = 0; i < currentLevel.row; i++) {
            for (let j = 0; j < currentLevel.col; j++) {
                if (cellCount % 2 === 0) {
                    randomType = Math.floor(Math.random() * (maxFruitType - minFruitType + 1)) + minFruitType;
                }
                this.createCell(i, j, randomType);
                cellCount++;
            }
        }
        
        this.shuffleCells();
        
        // 初始化连接系统（需要在棋盘创建后）
        this.connectionSystem = new ConnectionSystem(this.levelIndex, this.gridNode);

        // 初始化选择逻辑管理器
        this.selectionLogicManager = new SelectionLogicManager(this.connectionSystem, this.gridNode);

        // 初始化无解检测器
        this.deadlockDetector.initialize(this.connectionSystem, this, this.toastManager);

        // 游戏初始化完成后检测是否有可连接的棋子
        this.scheduleOnce(() => {
            this.deadlockDetector.checkForDeadlock(true);
        }, 0.5);
    }
    
    /**
     * 创建单个棋子
     */
    createCell(row: number, col: number, iconType: number) {
        const cellNode = lp(this.cellPrefab, this.gridNode);
        const currentLevel = gameData[this.levelIndex];
        
        // 设置位置
        const x = (col - (currentLevel.col - 1) / 2) * cellSpacing;
        const y = ((currentLevel.row - 1) / 2 - row) * cellSpacing;
        cellNode.setPosition(x, y, 0);
        
        // 设置棋子类型
        const cellComp = cellNode.getComponent(cellFixed);
        cellComp.setType(iconType);
        
        // 添加点击事件
        const button = cellNode.addComponent(Button);
        button.node.on(Button.EventType.CLICK, () => {
            this.onCellClick(cellNode);
        }, this);
        
        return cellNode;
    }
    
    /**
     * 洗牌算法
     * 重新随机排列棋盘上的棋子类型，保持类型数量和配对关系不变
     */
    public shuffleCells() {
        if (!this.gridNode || !this.gridNode.children) {
            LogManager.shuffleAlgorithm.warn("⚠️ 棋盘节点无效，无法执行洗牌");
            return;
        }

        const cellNodes = this.gridNode.children.filter(node => node.active && !node['isPathRenderer']);
        LogManager.shuffleAlgorithm.log(`洗牌前棋子数量: ${cellNodes.length}`);

        // 收集所有棋子的类型并统计
        const cellTypes: number[] = [];
        const beforeCounts: { [key: number]: number } = {};

        cellNodes.forEach(cellNode => {
            const cellComponent = cellNode.getComponent(cellFixed);
            if (cellComponent) {
                const type = cellComponent.getType();
                cellTypes.push(type);
                beforeCounts[type] = (beforeCounts[type] || 0) + 1;
            }
        });

        LogManager.shuffleAlgorithm.log("洗牌前类型分布:", beforeCounts);
        LogManager.shuffleAlgorithm.log("收集到的类型数组:", cellTypes);

        // 验证收集的类型数量是否正确
        if (cellTypes.length !== cellNodes.length) {
            LogManager.shuffleAlgorithm.error(`类型收集错误: 期望${cellNodes.length}个，实际${cellTypes.length}个`);
            return;
        }

        // 使用Fisher-Yates算法打乱类型数组
        for (let i = cellTypes.length - 1; i > 0; i--) {
            const randomIndex = Math.floor(Math.random() * (i + 1));
            // 交换数组中的元素
            [cellTypes[i], cellTypes[randomIndex]] = [cellTypes[randomIndex], cellTypes[i]];
        }

        LogManager.shuffleAlgorithm.log("打乱后类型数组:", cellTypes);

        // 将打乱后的类型重新分配给棋子
        cellNodes.forEach((cellNode, index) => {
            const cellComponent = cellNode.getComponent(cellFixed);
            if (cellComponent && index < cellTypes.length) {
                cellComponent.setType(cellTypes[index]);
            }
        });

        // 验证洗牌后的类型分布
        const afterCounts: { [key: number]: number } = {};
        cellNodes.forEach(cellNode => {
            const cellComponent = cellNode.getComponent(cellFixed);
            if (cellComponent) {
                const type = cellComponent.getType();
                afterCounts[type] = (afterCounts[type] || 0) + 1;
            }
        });

        console.log("� 洗牌后类型分布:", afterCounts);

        // 验证类型分布是否一致
        if (JSON.stringify(beforeCounts) === JSON.stringify(afterCounts)) {
            LogManager.shuffleAlgorithm.log("✅ 洗牌成功，类型分布保持一致");
        } else {
            LogManager.shuffleAlgorithm.error("❌ 洗牌失败，类型分布发生变化");
            LogManager.shuffleAlgorithm.error("洗牌前:", beforeCounts);
            LogManager.shuffleAlgorithm.error("洗牌后:", afterCounts);
        }
    }

    // ==================== 游戏交互逻辑 ====================
    
    /**
     * 棋子点击处理
     */
    onCellClick(cellNode: Node) {
        // 使用选择逻辑管理器处理点击
        this.selectionLogicManager.handleCellClick(cellNode, () => {
            // 棋子消除后检查游戏胜利
            this.checkGameWin();

            // 检查是否存在可连接的棋子对，如果不存在则自动打乱
            this.deadlockDetector.checkForDeadlock();
        });
    }

    
    /**
     * 检查游戏胜利
     */
    private checkGameWin() {
        if (!this.gridNode) {
            return;
        }

        // 统计还有多少活跃的棋子（排除路径渲染器节点）
        let activeCellCount = 0;
        this.gridNode.children.forEach(cellNode => {
            // 排除路径渲染器节点，只计算真正的棋子
            if (cellNode.active && !cellNode['isPathRenderer']) {
                activeCellCount++;
            }
        });

        LogManager.gameManager.log(`剩余棋子数量: ${activeCellCount}`);

        // 如果没有活跃的棋子，游戏胜利
        if (activeCellCount === 0) {
            this.onGameWin();
        }
    }
    
    /**
     * 游戏胜利处理
     */
    private onGameWin() {
        LogManager.gameManager.log("🎉 游戏胜利！所有棋子已消除");
        
        // 停止背景音乐并播放胜利音效
        if (this.audioManager) {
            this.audioManager.stopBGM();
            this.audioManager.playWinSound();
        }
        
        // 播放胜利动画（暂时注释，因为用户已注释）
        //AnimationSystem.playVictoryAnimation(this.node);
    }

    // ==================== 颜色映射系统 ====================
    
    /**
     * 获取图标类型对应的背景颜色索引
     */
    public static getBackgroundColorIndex(iconType: number): number {
        if (gameMgrSimplified.colorMapping.has(iconType)) {
            return gameMgrSimplified.colorMapping.get(iconType);
        }
        
        const colorIndex = gameMgrSimplified.colorAssignmentCounter % gameMgrSimplified.BACKGROUND_COLOR_COUNT;
        gameMgrSimplified.colorMapping.set(iconType, colorIndex);
        gameMgrSimplified.colorAssignmentCounter++;
        
        return colorIndex;
    }
    
    /**
     * 重置颜色映射
     */
    public static resetColorMapping() {
        gameMgrSimplified.colorMapping.clear();
        gameMgrSimplified.colorAssignmentCounter = 0;
    }
}
