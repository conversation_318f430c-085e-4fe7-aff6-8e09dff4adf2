import { _decorator, Node } from 'cc';
import { AnimationSystem } from './animationSystem';

/**
 * 震动系统 - 负责设备震动和视觉震动效果
 */
export class VibrationSystem {
    
    private static instance: VibrationSystem = null;
    private vibrationEnabled: boolean = true;
    private isInitialized: boolean = false;
    
    constructor() {
        if (VibrationSystem.instance === null) {
            VibrationSystem.instance = this;
        }
        return VibrationSystem.instance;
    }
    
    /**
     * 获取震动系统单例
     */
    public static getInstance(): VibrationSystem {
        if (VibrationSystem.instance === null) {
            VibrationSystem.instance = new VibrationSystem();
        }
        return VibrationSystem.instance;
    }
    
    /**
     * 初始化震动系统
     */
    public initialize(): void {
        this.checkVibrationSupport();
        this.isInitialized = true;
        console.log("🔧 震动系统已初始化");
    }
    
    /**
     * 播放震动效果
     * @param pattern 震动模式数组 [震动时间, 停止时间, 震动时间, ...]
     * @param fallbackNode 当设备不支持震动时的降级节点（播放视觉震动）
     */
    public vibrate(pattern: number[] = [100, 50, 100], fallbackNode?: Node): void {
        if (!this.vibrationEnabled) {
            console.log("震动已被用户禁用");
            return;
        }
        
        console.log("📳 播放震动效果");
        
        if (this.checkVibrationSupport()) {
            try {
                const result = navigator.vibrate(pattern);
                
                if (result) {
                    console.log("✓ 震动效果已触发");
                } else {
                    console.log("⚠️ 震动被阻止或失败");
                    this.playVisualFallback(fallbackNode);
                }
            } catch (error) {
                console.error("❌ 震动API调用失败:", error);
                this.playVisualFallback(fallbackNode);
            }
        } else {
            console.log("⚠️ 震动不可用，使用视觉效果");
            this.playVisualFallback(fallbackNode);
        }
    }
    
    /**
     * 播放失败震动（相同棋子无法连接时）
     * @param fallbackNode 降级节点
     */
    public vibrateFailure(fallbackNode?: Node): void {
        this.vibrate([100, 50, 100], fallbackNode);
    }
    
    /**
     * 播放警告震动
     * @param fallbackNode 降级节点
     */
    public vibrateWarning(fallbackNode?: Node): void {
        this.vibrate([200], fallbackNode);
    }
    
    /**
     * 播放成功震动
     * @param fallbackNode 降级节点
     */
    public vibrateSuccess(fallbackNode?: Node): void {
        this.vibrate([50, 30, 50, 30, 50], fallbackNode);
    }
    
    /**
     * 停止震动
     */
    public stopVibration(): void {
        if (navigator && navigator.vibrate) {
            navigator.vibrate(0);
            console.log("⏹️ 震动已停止");
        }
    }
    
    /**
     * 启用或禁用震动
     * @param enabled 是否启用震动
     */
    public setVibrationEnabled(enabled: boolean): void {
        this.vibrationEnabled = enabled;
        console.log(`🔊 震动${enabled ? '启用' : '禁用'}`);
        
        if (!enabled) {
            this.stopVibration();
        }
    }
    
    /**
     * 获取震动启用状态
     */
    public isVibrationEnabled(): boolean {
        return this.vibrationEnabled;
    }
    
    /**
     * 检查震动支持情况
     */
    private checkVibrationSupport(): boolean {
        // 检查基本API支持
        if (!navigator || !navigator.vibrate) {
            console.log("❌ 浏览器不支持震动API");
            return false;
        }
        
        // 检查是否在安全环境
        const isSecure = location.protocol === 'https:' || 
                        location.hostname === 'localhost' || 
                        location.hostname === '127.0.0.1';
        
        if (!isSecure) {
            console.log("⚠️ 非安全环境，震动可能被限制");
        }
        
        // 检查用户代理（移动设备更可能支持震动）
        const userAgent = navigator.userAgent.toLowerCase();
        const isMobile = /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
        
        if (!isMobile) {
            console.log("⚠️ 非移动设备，震动效果可能有限");
        }
        
        console.log("震动环境检查:", {
            hasAPI: true,
            isSecure,
            isMobile,
            userAgent: navigator.userAgent
        });
        
        return true; // 即使环境不理想也尝试调用
    }
    
    /**
     * 播放视觉震动降级效果
     * @param targetNode 要震动的节点
     */
    private playVisualFallback(targetNode?: Node): void {
        if (targetNode) {
            AnimationSystem.playVisualShake(targetNode, 5, 0.3);
        } else {
            console.log("⚠️ 无降级节点，跳过视觉震动");
        }
    }
    
    /**
     * 测试震动功能
     * @param fallbackNode 降级节点
     */
    public testVibration(fallbackNode?: Node): void {
        console.log("🧪 测试震动功能");
        this.vibrate([200], fallbackNode);
    }
    
    /**
     * 获取震动系统状态信息
     */
    public getSystemInfo(): object {
        return {
            isInitialized: this.isInitialized,
            vibrationEnabled: this.vibrationEnabled,
            hasVibrationAPI: !!(navigator && navigator.vibrate),
            isSecureContext: location.protocol === 'https:' || location.hostname === 'localhost',
            userAgent: navigator.userAgent,
            isMobile: /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(navigator.userAgent.toLowerCase())
        };
    }
    
    /**
     * 打印震动系统状态（调试用）
     */
    public printSystemStatus(): void {
        console.log("=== 震动系统状态 ===");
        const info = this.getSystemInfo();
        Object.entries(info).forEach(([key, value]) => {
            console.log(`${key}: ${value}`);
        });
        console.log("==================");
    }
}
