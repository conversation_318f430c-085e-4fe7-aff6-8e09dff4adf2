import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;
import { audioMgr } from './audioMgr';
import { gameMgrSimplified } from './gameMgrSimplified';

@ccclass('loading')
export class loading extends Component {
    start() {
        //播放loading背景音乐
        const audioManager = audioMgr.getInstance();
        audioManager.playLoadingMusic();

    }

    update(deltaTime: number) {
        
    }
    onDestroy() {
        //停止播放loading背景音乐
        const audioManager = audioMgr.getInstance();
        audioManager.stopCurrentBGM();
        
    }
    startBtn() {
        this.node.destroy();
        console.log("loading界面已销毁");
        
        gameMgrSimplified.getInstance().enterGame();

    }
}


