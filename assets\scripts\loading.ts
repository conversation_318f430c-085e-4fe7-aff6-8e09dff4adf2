import { _decorator, Component, Node } from 'cc';
const { ccclass } = _decorator;
import { AudioManager } from './audioManager';
import { VibrationSystem } from './vibrationSystem';
import { gameMgrSimplified } from './gameMgrSimplified';

/**
 * Loading界面控制器
 * 负责loading界面的显示、音效播放和与游戏主界面的切换
 */
@ccclass('loading')
export class loading extends Component {

    // ==================== 系统引用 ====================
    private audioManager: AudioManager = null;
    private vibrationSystem: VibrationSystem = null;
    private gameManager: gameMgrSimplified = null;

    // ==================== 生命周期方法 ====================

    start() {
        console.log("🎮 Loading界面初始化");

        // 初始化系统引用
        this.initializeSystems();

        // 查找游戏管理器
        this.findGameManager();

        // 播放loading背景音乐
        this.playLoadingMusic();

        console.log("🎮 Loading界面已显示");
    }

    onDestroy() {
        console.log("🎮 Loading界面被销毁");

        // 停止播放loading背景音乐
        this.stopLoadingMusic();

        // 清理引用
        this.cleanupReferences();
    }

    // ==================== 初始化方法 ====================

    /**
     * 初始化系统引用
     */
    private initializeSystems() {
        this.audioManager = AudioManager.getInstance();
        this.vibrationSystem = VibrationSystem.getInstance();

        if (this.audioManager) {
            console.log("✅ 音频管理器已连接");
        } else {
            console.warn("⚠️ 音频管理器未找到");
        }

        if (this.vibrationSystem) {
            console.log("✅ 震动系统已连接");
        } else {
            console.warn("⚠️ 震动系统未找到");
        }
    }

    /**
     * 查找游戏管理器
     */
    private findGameManager() {
        this.gameManager = gameMgrSimplified.getInstance();

        if (this.gameManager) {
            console.log("✅ 找到游戏管理器");
        } else {
            console.error("❌ 未找到游戏管理器");
        }
    }

    // ==================== 音效控制 ====================

    /**
     * 播放loading背景音乐
     */
    private playLoadingMusic() {
        if (this.audioManager) {
            this.audioManager.playLoadingMusic();
            console.log("🎵 Loading音乐开始播放");
        }
    }

    /**
     * 停止loading背景音乐
     */
    private stopLoadingMusic() {
        if (this.audioManager) {
            this.audioManager.stopBGM();
            console.log("🎵 Loading音乐已停止");
        }
    }

    /**
     * 播放点击音效
     */
    private playClickSound() {
        if (this.audioManager) {
            this.audioManager.playClickSound();
            console.log("🔊 播放点击音效");
        }
    }

    // ==================== 震动控制 ====================

    /**
     * 触发震动反馈
     */
    private triggerVibration() {
        if (this.vibrationSystem) {
            this.vibrationSystem.vibrate([50]); // 50ms轻微震动
            console.log("📳 触发震动反馈");
        }
    }

    // ==================== 公共方法 ====================

    /**
     * 开始游戏按钮点击事件
     * 这是主要的入口函数，供按钮绑定使用
     */
    public startBtn() {
        console.log("🎮 点击开始游戏按钮");

        // 播放点击音效
        this.playClickSound();

        // 触发震动反馈
        this.triggerVibration();

        // 开始切换到游戏主界面
        this.startGameTransition();
    }

    /**
     * 开始游戏切换流程
     */
    private startGameTransition() {
        console.log("🔄 开始切换到游戏主界面");

        // 延迟一点时间让音效和震动完成
        this.scheduleOnce(() => {
            this.destroyLoadingAndStartGame();
        }, 0.1);
    }

    /**
     * 销毁loading界面并启动游戏
     */
    private destroyLoadingAndStartGame() {
        // 通知游戏管理器开始游戏
        if (this.gameManager) {
            console.log("🎮 通知游戏管理器开始游戏");
            this.gameManager.enterGame();
        } else {
            console.error("❌ 游戏管理器未找到，无法开始游戏");
        }

        // 销毁loading界面
        if (this.node && this.node.isValid) {
            this.node.destroy();
            console.log("✅ loading界面已销毁");
        }
    }

    // ==================== 清理方法 ====================

    /**
     * 清理引用
     */
    private cleanupReferences() {
        this.audioManager = null;
        this.vibrationSystem = null;
        this.gameManager = null;
    }
}


