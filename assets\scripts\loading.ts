import { _decorator, Component, Button } from 'cc';
const { ccclass, property } = _decorator;
import { AudioManager } from './audioManager';
import { VibrationSystem } from './vibrationSystem';
import { gameMgrSimplified } from './gameMgrSimplified';

/**
 * Loading界面控制器
 * 简单的loading界面，点击按钮后切换到游戏主界面
 */
@ccclass('loading')
export class loading extends Component {

    @property({ type: Button, displayName: "开始游戏按钮" })
    startButton: Button = null!;

    onLoad() {
        console.log("🎮 Loading组件onLoad");

        // 如果按钮已经绑定，设置点击事件
        if (this.startButton) {
            console.log("🎮 找到开始按钮，绑定点击事件");
            this.startButton.node.on('click', this.startBtn, this);
        } else {
            console.log("⚠️ 开始按钮未绑定，尝试自动查找");
            this.findStartButton();
        }
    }

    /**
     * 自动查找开始按钮
     */
    private findStartButton() {
        // 方法1：通过节点名称查找
        const buttonNode = this.node.getChildByName('StartButton') ||
                          this.node.getChildByName('startButton') ||
                          this.node.getChildByName('开始游戏') ||
                          this.node.getChildByName('StartBtn');

        if (buttonNode) {
            const button = buttonNode.getComponent(Button);
            if (button) {
                console.log("🎮 自动找到开始按钮:", buttonNode.name);
                this.startButton = button;
                button.node.on('click', this.startBtn, this);
                return;
            }
        }

        // 方法2：查找所有子节点中的Button组件
        const buttons = this.node.getComponentsInChildren(Button);
        if (buttons.length > 0) {
            console.log(`🎮 找到 ${buttons.length} 个按钮，使用第一个作为开始按钮`);
            this.startButton = buttons[0];
            buttons[0].node.on('click', this.startBtn, this);
        } else {
            console.error("❌ 未找到任何按钮组件");
        }
    }

    start() {
        console.log("🎮 Loading界面显示");
        console.log("🎮 Loading组件已启动，等待按钮点击...");

        // 播放loading音乐
        const audioManager = AudioManager.getInstance();
        if (audioManager) {
            audioManager.playLoadingMusic();
        }
    }

    onDestroy() {
        console.log("🎮 Loading界面销毁");
    }

    /**
     * 开始游戏按钮点击事件
     */
    public startBtn() {
        console.log("🎮 点击开始游戏按钮 - 方法被调用");
        console.log("🎮 当前节点:", this.node.name);

        // 播放点击音效和震动
        const audioManager = AudioManager.getInstance();
        const vibrationSystem = VibrationSystem.getInstance();

        console.log("🎮 音频管理器:", audioManager ? "已找到" : "未找到");
        console.log("🎮 震动系统:", vibrationSystem ? "已找到" : "未找到");

        if (audioManager) {
            audioManager.playClickSound();
        }

        if (vibrationSystem) {
            vibrationSystem.vibrate([50]);
        }

        // 通知游戏管理器开始游戏
        const gameManager = gameMgrSimplified.getInstance();
        console.log("🎮 游戏管理器:", gameManager ? "已找到" : "未找到");

        if (gameManager) {
            console.log("🎮 准备销毁Loading界面并启动游戏");

            // 销毁loading界面
            this.node.destroy();

            // 启动游戏
            gameManager.enterGame();
        } else {
            console.error("❌ 游戏管理器未找到，无法启动游戏");
        }
    }

    /**
     * 备用按钮点击方法（如果startBtn不工作，可以尝试这个）
     */
    public onStartButtonClick() {
        console.log("🎮 备用按钮方法被调用");
        this.startBtn();
    }

    /**
     * 另一个备用方法
     */
    public startGame() {
        console.log("🎮 startGame方法被调用");
        this.startBtn();
    }
}


