import { _decorator, Component } from 'cc';
const { ccclass } = _decorator;
import { AudioManager } from './audioManager';
import { VibrationSystem } from './vibrationSystem';
import { gameMgrSimplified } from './gameMgrSimplified';

/**
 * Loading界面控制器
 * 简单的loading界面，点击按钮后切换到游戏主界面
 */
@ccclass('loading')
export class loading extends Component {

    start() {
        console.log("🎮 Loading界面显示");

        // 播放loading音乐
        const audioManager = AudioManager.getInstance();
        if (audioManager) {
            audioManager.playLoadingMusic();
        }
    }

    onDestroy() {
        console.log("🎮 Loading界面销毁");
    }

    /**
     * 开始游戏按钮点击事件
     */
    public startBtn() {
        console.log("🎮 点击开始游戏按钮");

        // 播放点击音效和震动
        const audioManager = AudioManager.getInstance();
        const vibrationSystem = VibrationSystem.getInstance();

        if (audioManager) {
            audioManager.playClickSound();
        }

        if (vibrationSystem) {
            vibrationSystem.vibrate([50]);
        }

        // 销毁loading界面
        this.node.destroy();

        // 通知游戏管理器开始游戏
        const gameManager = gameMgrSimplified.getInstance();
        if (gameManager) {
            gameManager.enterGame();
        } else {
            console.error("❌ 游戏管理器未找到");
        }
    }
}


