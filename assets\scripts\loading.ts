import { _decorator, Component, Button, input, Input, EventKeyboard, KeyCode } from 'cc';
const { ccclass } = _decorator;
import { AudioManager } from './audioManager';
import { VibrationSystem } from './vibrationSystem';
import { gameMgrSimplified } from './gameMgrSimplified';

/**
 * Loading界面控制器
 * 简单的loading界面，点击按钮后切换到游戏主界面
 */
@ccclass('loading')
export class loading extends Component {

    private startButton: Button = null!;

    onLoad() {
        console.log("🎮 Loading组件onLoad");

        // 自动查找并绑定开始按钮
        this.findStartButton();
    }

    /**
     * 自动查找开始按钮
     */
    private findStartButton() {
        console.log("🎮 开始查找开始按钮...");

        // 方法1：通过节点名称查找（更多可能的名称）
        const possibleNames = [
            'StartButton', 'startButton', 'StartBtn', 'startBtn',
            '开始游戏', '开始', 'enterGame', 'EnterGame',
            'start', 'Start', 'begin', 'Begin'
        ];

        for (const name of possibleNames) {
            const buttonNode = this.node.getChildByName(name);
            if (buttonNode) {
                const button = buttonNode.getComponent(Button);
                if (button) {
                    console.log("🎮 通过名称找到开始按钮:", buttonNode.name);
                    this.startButton = button;
                    button.node.on('click', this.startBtn, this);
                    return;
                }
            }
        }

        // 方法2：递归查找所有子节点中的Button组件
        const buttons = this.node.getComponentsInChildren(Button);
        console.log(`🎮 在所有子节点中找到 ${buttons.length} 个按钮组件`);

        if (buttons.length > 0) {
            // 打印所有找到的按钮信息
            buttons.forEach((btn, index) => {
                console.log(`🎮 按钮 ${index + 1}: ${btn.node.name}`);
            });

            console.log(`🎮 使用第一个按钮作为开始按钮: ${buttons[0].node.name}`);
            this.startButton = buttons[0];
            buttons[0].node.on('click', this.startBtn, this);
        } else {
            console.error("❌ 未找到任何按钮组件");
            console.log("🎮 尝试添加全局点击监听作为备用方案");
        }
    }

    start() {
        console.log("🎮 Loading界面显示");
        console.log("🎮 Loading组件已启动，等待按钮点击...");

        // 播放loading音乐
        const audioManager = AudioManager.getInstance();
        if (audioManager) {
            audioManager.playLoadingMusic();
        }

        // 添加全局点击监听（备用方案）
        this.node.on('click', this.onNodeClick, this);
        console.log("🎮 已添加全局点击监听");

        // 添加键盘监听（备用方案）
        this.addKeyboardListener();
    }

    /**
     * 添加键盘监听
     */
    private addKeyboardListener() {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log("🎮 已添加键盘监听 (按空格键或回车键启动游戏)");
    }

    /**
     * 键盘按下事件
     */
    private onKeyDown(event: EventKeyboard) {
        if (event.keyCode === KeyCode.SPACE || event.keyCode === KeyCode.ENTER) {
            console.log("🎮 检测到键盘快捷键，启动游戏");
            this.startBtn();
        }
    }

    /**
     * 节点点击事件（备用方案）
     */
    private onNodeClick() {
        console.log("🎮 检测到节点点击事件，启动游戏");
        this.startBtn();
    }

    onDestroy() {
        console.log("🎮 Loading界面销毁");

        // 清理事件监听
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    /**
     * 开始游戏按钮点击事件
     */
    public startBtn() {
        console.log("🎮 点击开始游戏按钮");

        // 立即播放点击音效和震动
        const audioManager = AudioManager.getInstance();
        const vibrationSystem = VibrationSystem.getInstance();

        if (audioManager) {
            audioManager.playClickSound();
            // 立即停止loading音乐，准备切换到游戏音乐
            audioManager.stopBGM();
        }

        if (vibrationSystem) {
            vibrationSystem.vibrate([50]);
        }

        // 获取游戏管理器
        const gameManager = gameMgrSimplified.getInstance();
        if (!gameManager) {
            console.error("❌ 游戏管理器未找到");
            return;
        }

        console.log("🎮 开始优化的界面切换流程");

        // 优化的切换流程：分帧执行，避免卡顿
        this.performOptimizedTransition(gameManager);
    }

    /**
     * 执行优化的界面切换
     */
    private performOptimizedTransition(gameManager: gameMgrSimplified) {
        // 第1帧：立即开始游戏音频切换
        const audioManager = AudioManager.getInstance();
        if (audioManager) {
            audioManager.onGameStart();
        }

        // 立即开始加载游戏UI（在销毁loading界面之前）
        console.log("🎮 开始加载游戏界面");

        // 尝试多种加载方案
        try {
            console.log("🎮 尝试优化版本加载");
            gameManager.enterGameOptimized();
        } catch (error) {
            console.error("❌ 优化版本加载失败，尝试标准版本:", error);
            try {
                gameManager.enterGame();
            } catch (error2) {
                console.error("❌ 标准版本也失败，尝试简单版本:", error2);
                gameManager.enterGameSimple();
            }
        }

        // 延迟销毁loading界面，确保游戏界面已开始加载
        this.scheduleOnce(() => {
            console.log("🎮 销毁Loading界面");
            this.node.destroy();
        }, 0.3);
    }

    /**
     * 备用按钮点击方法（如果startBtn不工作，可以尝试这个）
     */
    public onStartButtonClick() {
        console.log("🎮 备用按钮方法被调用");
        this.startBtn();
    }

    /**
     * 另一个备用方法
     */
    public startGame() {
        console.log("🎮 startGame方法被调用");
        this.startBtn();
    }
}


