import { _decorator, Component } from 'cc';
const { ccclass } = _decorator;
import { AudioManager } from './audioManager';
import { VibrationSystem } from './vibrationSystem';
import { gameMgrSimplified } from './gameMgrSimplified';

/**
 * Loading界面控制器（兼容旧版本）
 * 建议使用StartGameUI替代此脚本
 */
@ccclass('loading')
export class loading extends Component {

    private audioManager: AudioManager = null;
    private vibrationSystem: VibrationSystem = null;

    start() {
        console.log("🎮 Loading界面初始化（旧版本）");

        // 初始化系统引用
        this.audioManager = AudioManager.getInstance();
        this.vibrationSystem = VibrationSystem.getInstance();

        // 播放loading背景音乐
        if (this.audioManager) {
            this.audioManager.playLoadingMusic();
        }
    }

    update(_deltaTime: number) {

    }

    onDestroy() {
        console.log("🎮 Loading界面被销毁（旧版本）");

        // 停止播放loading背景音乐
        if (this.audioManager) {
            this.audioManager.stopBGM();
        }

        // 清理引用
        this.audioManager = null;
        this.vibrationSystem = null;
    }

    /**
     * 开始按钮点击事件（兼容旧版本）
     */
    startBtn() {
        console.log("🎮 点击开始游戏按钮（旧版本）");

        // 播放点击音效
        if (this.audioManager) {
            this.audioManager.playClickSound();
        }

        // 触发震动反馈
        if (this.vibrationSystem) {
            this.vibrationSystem.vibrate([50]);
        }

        // 销毁loading界面
        this.node.destroy();
        console.log("✅ loading界面已销毁");

        // 通知游戏管理器开始游戏
        const gameManager = gameMgrSimplified.getInstance();
        if (gameManager) {
            gameManager.enterGame();
        } else {
            console.error("❌ 游戏管理器未找到，无法开始游戏");
        }
    }
}


