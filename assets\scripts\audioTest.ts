import { _decorator, Component, Node, Button, Label } from 'cc';
const { ccclass, property } = _decorator;
import { audioMgr } from './audioMgr';

/**
 * 音频系统测试脚本
 * 用于测试音频管理器的各种功能
 */
@ccclass('audioTest')
export class audioTest extends Component {
    
    @property({type: Node, displayName: "测试按钮容器"})
    buttonContainer: Node = null;
    
    @property({type: Label, displayName: "状态显示标签"})
    statusLabel: Label = null;
    
    private audioManager: audioMgr = null;
    
    onLoad() {
        // 获取音频管理器实例
        this.audioManager = audioMgr.getInstance();
        
        if (!this.audioManager) {
            console.error("⚠️ 音频管理器未找到！请确保场景中有音频管理器组件");
            return;
        }
        
        this.createTestButtons();
        this.updateStatusDisplay();
    }
    
    /**
     * 创建测试按钮
     */
    private createTestButtons() {
        if (!this.buttonContainer) {
            console.warn("⚠️ 按钮容器未设置");
            return;
        }
        
        // 创建各种测试按钮
        this.createButton("播放加载音乐", () => this.testLoadingMusic());
        this.createButton("播放游戏音乐", () => this.testGameplayBGM());
        this.createButton("暂停BGM", () => this.testPauseBGM());
        this.createButton("恢复BGM", () => this.testResumeBGM());
        this.createButton("停止BGM", () => this.testStopBGM());
        
        this.createButton("点击音效", () => this.testClickSound());
        this.createButton("成功音效", () => this.testMatchSound());
        this.createButton("失败音效", () => this.testFailSound());
        this.createButton("胜利音效", () => this.testWinSound());
        
        this.createButton("BGM音量+", () => this.testBGMVolumeUp());
        this.createButton("BGM音量-", () => this.testBGMVolumeDown());
        this.createButton("SFX音量+", () => this.testSFXVolumeUp());
        this.createButton("SFX音量-", () => this.testSFXVolumeDown());
        
        this.createButton("切换音频开关", () => this.testToggleAudio());
        this.createButton("打印状态", () => this.testPrintStatus());
    }
    
    /**
     * 创建单个测试按钮
     */
    private createButton(text: string, callback: () => void) {
        const buttonNode = new Node(text);
        buttonNode.parent = this.buttonContainer;
        
        const button = buttonNode.addComponent(Button);
        const label = buttonNode.addComponent(Label);
        
        label.string = text;
        label.fontSize = 20;
        
        button.node.on(Button.EventType.CLICK, () => {
            callback();
            this.updateStatusDisplay();
        }, this);
    }
    
    /**
     * 更新状态显示
     */
    private updateStatusDisplay() {
        if (this.statusLabel && this.audioManager) {
            this.statusLabel.string = this.audioManager.getAudioInfo();
        }
    }
    
    // ==================== 测试方法 ====================
    
    private testLoadingMusic() {
        console.log("🧪 测试：播放加载音乐");
        this.audioManager.playLoadingMusic();
    }
    
    private testGameplayBGM() {
        console.log("🧪 测试：播放游戏音乐");
        this.audioManager.playGameplayBGM();
    }
    
    private testPauseBGM() {
        console.log("🧪 测试：暂停BGM");
        this.audioManager.pauseBGM();
    }
    
    private testResumeBGM() {
        console.log("🧪 测试：恢复BGM");
        this.audioManager.resumeBGM();
    }
    
    private testStopBGM() {
        console.log("🧪 测试：停止BGM");
        this.audioManager.stopCurrentBGM();
    }
    
    private testClickSound() {
        console.log("🧪 测试：点击音效");
        this.audioManager.playClickSound();
    }
    
    private testMatchSound() {
        console.log("🧪 测试：成功音效");
        this.audioManager.playMatchSound();
    }
    
    private testFailSound() {
        console.log("🧪 测试：失败音效");
        this.audioManager.playFailSound();
    }
    
    private testWinSound() {
        console.log("🧪 测试：胜利音效");
        this.audioManager.playWinSound();
    }
    
    private testBGMVolumeUp() {
        const currentVolume = this.audioManager['bgmVolume'];
        const newVolume = Math.min(1.0, currentVolume + 0.1);
        console.log(`🧪 测试：BGM音量增加到 ${newVolume.toFixed(1)}`);
        this.audioManager.setBGMVolume(newVolume);
    }
    
    private testBGMVolumeDown() {
        const currentVolume = this.audioManager['bgmVolume'];
        const newVolume = Math.max(0.0, currentVolume - 0.1);
        console.log(`🧪 测试：BGM音量减少到 ${newVolume.toFixed(1)}`);
        this.audioManager.setBGMVolume(newVolume);
    }
    
    private testSFXVolumeUp() {
        const currentVolume = this.audioManager['sfxVolume'];
        const newVolume = Math.min(1.0, currentVolume + 0.1);
        console.log(`🧪 测试：SFX音量增加到 ${newVolume.toFixed(1)}`);
        this.audioManager.setSFXVolume(newVolume);
    }
    
    private testSFXVolumeDown() {
        const currentVolume = this.audioManager['sfxVolume'];
        const newVolume = Math.max(0.0, currentVolume - 0.1);
        console.log(`🧪 测试：SFX音量减少到 ${newVolume.toFixed(1)}`);
        this.audioManager.setSFXVolume(newVolume);
    }
    
    private testToggleAudio() {
        const currentState = this.audioManager['audioEnabled'];
        console.log(`🧪 测试：切换音频开关 ${currentState ? 'OFF' : 'ON'}`);
        this.audioManager.setAudioEnabled(!currentState);
    }
    
    private testPrintStatus() {
        console.log("🧪 测试：打印音频状态");
        this.audioManager.printAudioStatus();
    }
}
