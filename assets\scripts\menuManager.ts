import { _decorator, Component, Node, Button } from 'cc';
const { ccclass, property } = _decorator;
import { gameMgrSimplified } from './gameMgrSimplified';

/**
 * 菜单管理器
 * 专门处理菜单预制体中的按钮事件
 * 解决预制体编辑器中无法绑定外部脚本函数的问题
 */
@ccclass('menuManager')
export class menuManager extends Component {
    
    @property({ type: Button, displayName: "打乱按钮" })
    shuffleButton: Button = null!;
    
    @property({ type: Button, displayName: "重新开始按钮" })
    restartButton: Button = null!;
    
    @property({ type: Button, displayName: "暂停按钮" })
    pauseButton: Button = null!;
    
    @property({ type: Button, displayName: "设置按钮" })
    settingsButton: Button = null!;

    @property({ type: Button, displayName: "提示按钮" })
    hintButton: Button = null!;
    
    // 游戏管理器引用
    private gameManager: gameMgrSimplified = null!;
    
    onLoad() {
        this.setupButtons();
        this.findGameManager();
    }
    
    /**
     * 设置按钮事件监听
     */
    private setupButtons() {
        // 打乱按钮
        if (this.shuffleButton) {
            this.shuffleButton.node.on(Button.EventType.CLICK, this.onShuffleClick, this);
        }
        
        // 重新开始按钮
        if (this.restartButton) {
            this.restartButton.node.on(Button.EventType.CLICK, this.onRestartClick, this);
        }
        
        // 暂停按钮
        if (this.pauseButton) {
            this.pauseButton.node.on(Button.EventType.CLICK, this.onPauseClick, this);
        }
        
        // 设置按钮
        if (this.settingsButton) {
            this.settingsButton.node.on(Button.EventType.CLICK, this.onSettingsClick, this);
        }

        // 提示按钮
        if (this.hintButton) {
            this.hintButton.node.on(Button.EventType.CLICK, this.onHintClick, this);
        }

        console.log("🎮 菜单按钮事件已设置");
    }
    
    /**
     * 查找游戏管理器
     */
    private findGameManager() {
        // 方法1：从父节点查找
        this.gameManager = this.node.parent?.getComponent(gameMgrSimplified) ||
                          this.node.parent?.parent?.getComponent(gameMgrSimplified);
        
        // 方法2：从场景根节点查找
        if (!this.gameManager) {
            const scene = this.node.scene;
            if (scene) {
                this.gameManager = scene.getComponentInChildren(gameMgrSimplified);
            }
        }
        
        // 方法3：通过节点名称查找
        if (!this.gameManager) {
            const canvas = this.node.scene?.getChildByName('Canvas');
            if (canvas) {
                this.gameManager = canvas.getComponent(gameMgrSimplified);
            }
        }
        
        if (this.gameManager) {
            console.log("✅ 找到游戏管理器");
        } else {
            console.error("❌ 未找到游戏管理器");
        }
    }
    
    // ==================== 按钮事件处理方法 ====================
    
    /**
     * 打乱按钮点击事件
     */
    private onShuffleClick() {
        console.log("🔀 点击打乱按钮");
        
        if (this.gameManager) {
            this.gameManager.shuffleCells();
        } else {
            console.error("❌ 游戏管理器未找到，无法执行打乱");
        }
    }
    
    /**
     * 重新开始按钮点击事件
     */
    private onRestartClick() {
        console.log("🔄 点击重新开始按钮");
        
        if (this.gameManager) {
            this.gameManager.initGrid();
        } else {
            console.error("❌ 游戏管理器未找到，无法重新开始");
        }
    }
    
    /**
     * 暂停按钮点击事件
     */
    private onPauseClick() {
        console.log("⏸️ 点击暂停按钮");
        
        // 这里可以添加暂停游戏的逻辑
        // 例如：暂停动画、显示暂停菜单等
    }
    
    /**
     * 设置按钮点击事件
     */
    private onSettingsClick() {
        console.log("⚙️ 点击设置按钮");

        // 这里可以添加打开设置界面的逻辑
        // 例如：显示设置面板、音效开关等
    }

    /**
     * 提示按钮点击事件
     */
    private onHintClick() {
        console.log("💡 点击提示按钮");

        if (this.gameManager && this.gameManager['selectionLogicManager']) {
            const selectionManager = this.gameManager['selectionLogicManager'];

            // 显示提示
            const hintShown = selectionManager.showHint();

            if (hintShown) {
                console.log("✅ 提示已显示");
                // 提示按钮不显示Toast，只在控制台输出
            } else {
                console.log("❌ 没有可连接的棋子对");

                // 触发无解检测和自动打乱（会显示相应的Toast）
                if (this.gameManager['deadlockDetector']) {
                    this.gameManager['deadlockDetector'].forceShuffle();
                }
            }
        } else {
            console.error("❌ 游戏管理器或选择管理器未找到，无法显示提示");
            // 无法获取Toast管理器时只输出日志
            console.log("系统错误，无法显示提示");
        }
    }
    
    // ==================== 公共方法 ====================
    
    /**
     * 手动设置游戏管理器引用
     * @param gameManager 游戏管理器实例
     */
    public setGameManager(gameManager: gameMgrSimplified) {
        this.gameManager = gameManager;
        console.log("✅ 手动设置游戏管理器成功");
    }
    
    /**
     * 启用/禁用所有按钮
     * @param enabled 是否启用
     */
    public setButtonsEnabled(enabled: boolean) {
        const buttons = [this.shuffleButton, this.restartButton, this.pauseButton, this.settingsButton, this.hintButton];

        buttons.forEach(button => {
            if (button) {
                button.interactable = enabled;
            }
        });

        console.log(`🎮 菜单按钮已${enabled ? '启用' : '禁用'}`);
    }
    
    /**
     * 获取调试信息
     */
    public getDebugInfo(): string {
        const buttonStatus = {
            shuffle: !!this.shuffleButton,
            restart: !!this.restartButton,
            pause: !!this.pauseButton,
            settings: !!this.settingsButton,
            hint: !!this.hintButton
        };

        return `菜单管理器状态: 游戏管理器${this.gameManager ? '已连接' : '未连接'}, ` +
               `按钮状态: ${JSON.stringify(buttonStatus)}`;
    }
}
